#!/bin/bash

# 安装已下载的 Go 1.24.5
set -e

echo "🚀 安装 Go 1.24.5"
echo "=================="

# 检查文件是否存在
if [ ! -f "go1.24.5.linux-arm64.tar.gz" ]; then
    echo "❌ 找不到 go1.24.5.linux-arm64.tar.gz 文件"
    exit 1
fi

echo "📋 当前 Go 版本:"
go version 2>/dev/null || echo "Go 未安装"

echo "🗑️  删除旧版本..."
sudo rm -rf /usr/local/go

echo "📁 解压 Go 1.24.5..."
sudo tar -C /usr/local -xzf go1.24.5.linux-arm64.tar.gz

echo "🔧 设置环境变量..."
# 添加到 .bashrc
if ! grep -q "/usr/local/go/bin" ~/.bashrc 2>/dev/null; then
    echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
    echo "✅ 已添加到 ~/.bashrc"
fi

# 添加到 .profile
if ! grep -q "/usr/local/go/bin" ~/.profile 2>/dev/null; then
    echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.profile
    echo "✅ 已添加到 ~/.profile"
fi

# 立即设置当前会话的环境变量
export PATH=$PATH:/usr/local/go/bin

echo "✅ Go 1.24.5 安装完成！"
echo ""
echo "📋 验证安装:"
/usr/local/go/bin/go version

echo ""
echo "🔄 环境变量已设置，新的 Go 路径:"
echo "   /usr/local/go/bin/go"
echo ""
echo "💡 如果 'go version' 仍显示旧版本，请运行:"
echo "   source ~/.bashrc"
echo "   或重新打开终端"
