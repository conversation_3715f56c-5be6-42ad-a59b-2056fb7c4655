#!/bin/bash

# Go 升级脚本
set -e

echo "🚀 升级 Go 到最新版本"
echo "========================"

# 检查当前 Go 版本
echo "📋 当前 Go 版本:"
go version 2>/dev/null || echo "Go 未安装"

# 检测系统架构
ARCH=$(uname -m)
case $ARCH in
    x86_64)
        GO_ARCH="amd64"
        ;;
    aarch64|arm64)
        GO_ARCH="arm64"
        ;;
    *)
        echo "❌ 不支持的架构: $ARCH"
        exit 1
        ;;
esac

echo "📊 系统架构: $ARCH -> Go架构: $GO_ARCH"

# Go 版本
GO_VERSION="1.24.0"
GO_FILE="go${GO_VERSION}.linux-${GO_ARCH}.tar.gz"
GO_URL="https://go.dev/dl/${GO_FILE}"

echo "📦 下载 Go ${GO_VERSION}..."
wget -O "/tmp/${GO_FILE}" "${GO_URL}"

echo "🗑️  删除旧版本..."
sudo rm -rf /usr/local/go

echo "📁 解压新版本..."
sudo tar -C /usr/local -xzf "/tmp/${GO_FILE}"

echo "🔧 设置环境变量..."
# 添加到 .bashrc
if ! grep -q "/usr/local/go/bin" ~/.bashrc; then
    echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
fi

# 添加到 .profile
if ! grep -q "/usr/local/go/bin" ~/.profile; then
    echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.profile
fi

echo "🧹 清理下载文件..."
rm -f "/tmp/${GO_FILE}"

echo "✅ Go 升级完成！"
echo "🔄 请运行以下命令重新加载环境变量:"
echo "   source ~/.bashrc"
echo "   或者重新打开终端"
echo ""
echo "📋 验证安装:"
echo "   /usr/local/go/bin/go version"
