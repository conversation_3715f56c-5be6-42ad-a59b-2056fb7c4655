#!/bin/bash

# DigWis Panel 开发环境启动脚本
# 设置环境变量并启动 Air

set -e

echo "🚀 启动 DigWis Panel 开发环境"
echo "=================================="

# 设置环境变量
export PATH=$HOME/local/go/bin:$HOME/local/node-v20.18.0-linux-arm64/bin:$HOME/local:$PATH
export GOPATH=$HOME/go

echo "✅ 环境变量设置完成"
echo "📁 Go 路径: $(which go)"
echo "📁 Node 路径: $(which node)"
echo "📁 npm 路径: $(which npm)"
echo "=================================="

# 启动 Air
echo "🔥 启动 Air 热重载..."

# 尝试找到air命令
AIR_CMD=""
if command -v air >/dev/null 2>&1; then
    AIR_CMD="air"
elif [ -f "$HOME/local/air" ]; then
    AIR_CMD="$HOME/local/air"
elif [ -f "$HOME/go/bin/air" ]; then
    AIR_CMD="$HOME/go/bin/air"
else
    echo "❌ 未找到 air 命令"
    echo "💡 尝试安装 air: go install github.com/air-verse/air@latest"
    echo "🔄 使用替代方案启动开发服务器..."

    # 使用替代方案
    if [ -f "./dev_watch.sh" ]; then
        echo "📁 使用自定义文件监控脚本..."
        exec ./dev_watch.sh
    else
        echo "🚀 直接启动服务器..."
        go run main.go -port 9090 -host 0.0.0.0
    fi
    exit 0
fi

echo "✅ 使用 Air: $AIR_CMD"
$AIR_CMD
